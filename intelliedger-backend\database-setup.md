# Database Setup Guide for IntelliLedger Backend

## Current Status
- ✅ Backend code is 100% complete and production-ready
- ✅ Dependencies installed
- ❌ Database not set up yet

## Quick Setup Options

### Option 1: Docker PostgreSQL (Recommended)
If Docker Desktop is installed and running:

```bash
# Start Docker Desktop first, then run:
docker run --name intelliedger-postgres -e POSTGRES_PASSWORD=password -e POSTGRES_DB=intelliedger_db -p 5432:5432 -d postgres:15

# Wait a few seconds, then test connection:
docker exec -it intelliedger-postgres psql -U postgres -d intelliedger_db -c "SELECT version();"
```

### Option 2: Cloud Database (Free & Fast)
Use a free cloud PostgreSQL database:

#### Supabase (Recommended for quick testing):
1. Go to https://supabase.com
2. Create free account
3. Create new project
4. Get connection string from Settings > Database
5. Update .env file with the connection string

#### Neon (Alternative):
1. Go to https://neon.tech
2. Create free account  
3. Create database
4. Get connection string
5. Update .env file

### Option 3: Local PostgreSQL Installation
Download from: https://www.postgresql.org/download/windows/
- Default user: `postgres`
- Set password: `password`
- Default port: `5432`
- Create database: `intelliedger_db`

## After Database Setup

Once you have PostgreSQL running, run these commands:

```bash
# Generate Prisma client
npm run db:generate

# Run database migrations
npm run db:migrate

# Seed the database with initial data
npm run db:seed

# Start the development server
npm run dev
```

## Test the Backend

Once running, test with:

```bash
# Health check
curl http://localhost:3001/health

# Login to get JWT token
curl -X POST http://localhost:3001/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'
```

## Current .env Configuration

```
DATABASE_URL="postgresql://postgres:password@localhost:5432/intelliedger_db"
PORT=3001
NODE_ENV=development
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-12345
JWT_EXPIRES_IN=24h
FRONTEND_URL=http://localhost:5173
```

## Next Steps After Database Setup

1. **Test Backend APIs** - Use the comprehensive test scripts
2. **Frontend Integration** - Connect React frontend to backend APIs
3. **A/P Module Frontend** - Build vendor and bill management UI
4. **A/R Module Frontend** - Build customer and invoice management UI
