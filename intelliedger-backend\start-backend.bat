@echo off
echo Starting IntelliLedger Backend Setup...
echo.

echo Step 1: Generating Prisma Client...
call npm run db:generate
if %errorlevel% neq 0 (
    echo ERROR: Failed to generate Prisma client
    echo Make sure DATABASE_URL in .env is correct
    pause
    exit /b 1
)

echo.
echo Step 2: Running Database Migrations...
call npm run db:migrate
if %errorlevel% neq 0 (
    echo ERROR: Failed to run migrations
    echo Make sure PostgreSQL is running and accessible
    pause
    exit /b 1
)

echo.
echo Step 3: Seeding Database with Initial Data...
call npm run db:seed
if %errorlevel% neq 0 (
    echo ERROR: Failed to seed database
    pause
    exit /b 1
)

echo.
echo Step 4: Starting Development Server...
echo Backend will be available at: http://localhost:3001
echo Health check: http://localhost:3001/health
echo.
echo Default admin login:
echo Email: <EMAIL>
echo Password: admin123
echo.
call npm run dev
